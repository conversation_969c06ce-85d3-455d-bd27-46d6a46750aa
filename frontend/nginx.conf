server {
        listen       80;
        server_name  manager.swan.dataslive.com;
        index index.html index.htm index.php;
        client_max_body_size 50m;
        location /
        {
           # 前端打包后的静态目录
           alias /data/app/swan_admin/frontend/base/dist/;
           #解决页面刷新404问题
           try_files $uri $uri/ /index.html;
        }
         location /ai
        {
           # 前端打包后的静态目录
           alias /data/app/swan_admin/frontend/ai/dist/;
           #解决页面刷新404问题
           try_files $uri $uri/ /index.html;
        }
         location /bi
        {
           # 前端打包后的静态目录
           alias /data/app/swan_admin/frontend/bi/dist/;
           #解决页面刷新404问题
           try_files $uri $uri/ /index.html;
        }
        location /toolbox
        {
           # 前端打包后的静态目录
           alias /data/app/swan_admin/frontend/tool/dist/;
           #解��页面刷新404问题
           try_files $uri $uri/ /index.html;
        }
        location /api {
                root /data/app/swan_admin/public;
                try_files $uri $uri/ /index.php?$query_string;
        }
        location /base/static {
                alias /data/app/swan_admin/frontend/base/dist/static;
        }
        location /ai/static {
                alias /data/app/swan_admin/frontend/ai/dist/static;
        }
        location /bi/static {
                alias /data/app/swan_admin/frontend/bi/dist/static;
        }
        location /toolbox/static/ {
                alias /data/app/swan_admin/frontend/tool/dist/static/;
        }

        location ~ \.php$ {
                root /data/app/swan_admin/public;
                fastcgi_pass   127.0.0.1:9000;
                fastcgi_index  index.php;
                fastcgi_param  APP_ENV  online;
                include fastcgi_params;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        }
 }
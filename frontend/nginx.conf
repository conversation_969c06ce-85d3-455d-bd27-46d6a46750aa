server {
        listen       80;
        server_name  manager.swan.dataslive.com;
        index index.html index.htm index.php;
        client_max_body_size 50m;
        # 具体平台路径优先匹配
        # toolbox平台 - 解决301重定向问题
        location /toolbox/ {
           # tool平台静态目录 (注意location后面有斜杠)
           alias /data/app/swan_admin/frontend/tool/dist/;
           try_files $uri $uri/ /toolbox/index.html;
        }
        location = /toolbox {
           # 不带斜杠的toolbox重定向到带斜杠的
           return 301 /toolbox/;
        }

        location /ai/ {
           # ai平台静态目录
           alias /data/app/swan_admin/frontend/ai/dist/;
           try_files $uri $uri/ /ai/index.html;
        }
        location = /ai {
           return 301 /ai/;
        }

        location /bi/ {
           # bi平台静态目录
           alias /data/app/swan_admin/frontend/bi/dist/;
           try_files $uri $uri/ /bi/index.html;
        }
        location = /bi {
           return 301 /bi/;
        }
        # base平台作为默认fallback
        location / {
           # base平台静态目录
           alias /data/app/swan_admin/frontend/base/dist/;
           try_files $uri $uri/ /index.html;
        }

        location /api {
                root /data/app/swan_admin/public;
                try_files $uri $uri/ /index.php?$query_string;
        }
        location /base/static {
                alias /data/app/swan_admin/frontend/base/dist/static;
        }
        location /ai/static {
                alias /data/app/swan_admin/frontend/ai/dist/static;
        }
        location /bi/static {
                alias /data/app/swan_admin/frontend/bi/dist/static;
        }
        location /toolbox/static/ {
                alias /data/app/swan_admin/frontend/tool/dist/static/;
        }

        location ~ \.php$ {
                root /data/app/swan_admin/public;
                fastcgi_pass   127.0.0.1:9000;
                fastcgi_index  index.php;
                fastcgi_param  APP_ENV  online;
                include fastcgi_params;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        }
 }
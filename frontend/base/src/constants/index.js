import { AimOutlined, BugOutlined, ToolOutlined } from "@ant-design/icons";
// 权限、菜单接口platform字段的传参
export const PLATFORM = 0;

// 接口api
let BASE_URL = "http://swan.admin.oversea.cmcm.com/api";
if (window.location.href.indexOf("manager.swan.dataslive.com") > -1) {
  BASE_URL = "http://manager.swan.dataslive.com/api";
} else if (window.location.href.indexOf("manager.swan.test.com") > -1) {
  BASE_URL = "http://manager.swan.test.com/api";
} else if (
  window.location.href.indexOf("127.0.0.1") > -1 ||
  window.location.href.indexOf("localhost") > -1
) {
  BASE_URL = "/api";
}
export const api = BASE_URL;

// 系统名称
export const system_name = "鸿鹄管理系统";

//
export const systemInfo = [
  {
    name: "AI平台",
    url: "/ai",
    value: 2,
    icon: AimOutlined,
  },
  {
    name: "BI平台",
    url: "/bi",
    value: 1,
    icon: BugOutlined,
  },
  {
    name: "Tool平台",
    url: "/toolbox/",
    value: 3,
    icon: ToolOutlined,
  },
];

#!/bin/bash

# 前端多平台构建脚本
# 用于构建所有平台并整合到统一的dist目录

set -e  # 遇到错误立即退出

echo "🚀 开始构建所有前端平台..."

# 清理旧的构建产物
echo "🧹 清理旧的构建产物..."
rm -rf dist
mkdir -p dist

# 构建各个平台
platforms=("base" "ai" "bi" "tool")

for platform in "${platforms[@]}"; do
    echo "📦 构建 $platform 平台..."
    cd $platform
    
    # 安装依赖（如果需要）
    if [ ! -d "node_modules" ]; then
        echo "📥 安装 $platform 平台依赖..."
        npm install
    fi
    
    # 构建
    npm run build
    
    cd ..
    
    echo "✅ $platform 平台构建完成"
done

# 整合构建产物到统一dist目录
echo "🔗 整合构建产物到统一dist目录..."

# 复制base平台作为根目录（默认首页）
echo "📋 复制base平台到根目录..."
cp -r base/dist/* dist/

# 复制各个子平台
for platform in "${platforms[@]}"; do
    if [ "$platform" != "base" ]; then
        echo "📋 复制 $platform 平台..."
        
        # 根据平台的homepage配置确定目标目录
        case $platform in
            "ai")
                target_dir="dist/ai"
                ;;
            "bi") 
                target_dir="dist/bi"
                ;;
            "tool")
                target_dir="dist/toolbox"  # tool平台使用toolbox路径
                ;;
        esac
        
        mkdir -p $target_dir
        cp -r $platform/dist/* $target_dir/
    fi
done

echo "🎉 所有平台构建完成！"
echo "📁 构建产物位置: frontend/dist/"
echo ""
echo "📋 目录结构:"
echo "├── dist/"
echo "│   ├── index.html          # base平台首页"
echo "│   ├── static/             # base平台静态资源"
echo "│   ├── ai/                 # ai平台"
echo "│   ├── bi/                 # bi平台"
echo "│   └── toolbox/            # tool平台"
echo ""
echo "🌐 现在可以通过以下路径访问各个平台:"
echo "   - http://your-domain/           # base平台"
echo "   - http://your-domain/ai         # ai平台"  
echo "   - http://your-domain/bi         # bi平台"
echo "   - http://your-domain/toolbox    # tool平台"

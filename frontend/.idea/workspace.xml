<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e62794dc-4320-4678-ac08-41a34dcc0770" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/ai/dist/asset-manifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/favicon.ico" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/manifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/robots.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/1.57cc91a9.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/10.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/11.42e675c1.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/12.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/13.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/14.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/15.42e675c1.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/16.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/17.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/18.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/19.42e675c1.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/20.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/21.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/22.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/23.42e675c1.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/24.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/25.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/26.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/27.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/28.7beb9be1.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/29.d20c2143.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/3.bb25082b.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/30.2193dfc3.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/31.2193dfc3.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/32.e10e7bd2.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/33.72f72ba8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/34.78473d82.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/35.976ab536.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/36.8a8111b7.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/37.9305122c.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/38.096261d4.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/39.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/4.0a1c66bf.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/40.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/41.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/42.5e545a5b.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/43.0d9d2e74.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/44.0d9d2e74.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/45.de6539fa.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/46.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/47.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/48.dbb8c655.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/49.0ba9133e.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/5.9305122c.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/50.e2cf74ba.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/51.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/52.80bb7ac8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/53.dbb8c655.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/54.dbb8c655.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/55.371c68f0.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/56.371c68f0.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/57.47de02f7.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/58.4e72d1da.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/59.2d2117ac.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/60.df20f229.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/61.3ccb1c4e.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/8.c87c170a.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/9.c4da0cff.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/css/main.f5e7b744.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/0.a4dc57ba.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/1.bed45c94.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/10.49d53d00.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/10.49d53d00.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/11.989c9abb.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/11.989c9abb.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/12.bac119e3.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/12.bac119e3.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/13.c994341a.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/13.c994341a.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/14.24195347.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/14.24195347.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/15.637de47d.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/15.637de47d.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/16.d5b78930.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/16.d5b78930.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/17.ad7574d5.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/17.ad7574d5.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/18.1c549764.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/18.1c549764.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/19.1481f836.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/19.1481f836.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/2.8afd97ab.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/20.85f34dd2.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/20.85f34dd2.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/21.808195e0.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/21.808195e0.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/22.096675eb.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/22.096675eb.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/23.09c75ff9.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/23.09c75ff9.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/24.7a0962dd.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/24.7a0962dd.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/25.2fae21a7.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/25.2fae21a7.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/26.71f1a826.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/26.71f1a826.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/27.2a5e8d7f.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/27.2a5e8d7f.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/28.53d2498f.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/28.53d2498f.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/29.2cad6dcb.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/3.a0953ab0.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/30.7e94dd26.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/31.6641d242.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/32.a72c95fb.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/33.d5f41175.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/34.6f818ec8.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/35.1cff411b.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/36.cd73bddd.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/37.71f674f6.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/38.2ce5bde6.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/39.624da7a2.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/4.9735bce2.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/40.150f0af9.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/41.79f830ee.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/42.8a6f0a84.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/43.0534af40.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/44.f2dbb956.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/45.c31b0775.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/46.bb906fe0.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/47.e3c4388a.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/48.2480bd05.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/49.230a306e.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/5.815aed2d.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/50.a1183f0e.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/51.7d10c368.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/52.2cd474d9.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/53.4cc73429.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/54.5db6f48a.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/55.e83bdad5.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/56.5ee78bf8.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/57.09dd8c0d.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/58.ab81a76e.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/59.25a45d1b.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/60.7b6c6865.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/61.24ab9c22.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/8.7afc9a7a.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/8.7afc9a7a.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/9.85f58eb1.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/main.93cf3d3e.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/js/runtime-main.43a55435.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ai/dist/static/media/bg.ebcb9160.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/asset-manifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/favicon.ico" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/manifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/robots.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/css/2.c466c0c3.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/css/3.8b277f8d.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/css/4.dc6cfdbc.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/css/5.416b6153.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/css/6.47de02f7.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/css/7.2d2117ac.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/css/8.df20f229.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/css/9.7ed8e2bf.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/css/main.f5e7b744.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/js/2.5c2e3ea1.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/js/2.5c2e3ea1.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/js/3.dc17f972.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/js/4.231decc0.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/js/5.6891dabd.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/js/6.a2b7044f.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/js/7.7bdfffa0.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/js/8.b65bec49.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/js/9.56d3669b.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/js/main.8b6ac138.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/js/runtime-main.97f5d2d4.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/dist/static/media/bg.ebcb9160.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/base/src/constants/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/base/src/constants/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/base/src/pages/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/base/src/pages/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/asset-manifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/favicon.ico" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/manifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/robots.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/1.aefd2a8b.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/10.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/11.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/12.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/13.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/14.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/15.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/16.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/17.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/18.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/19.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/20.5b507eb3.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/21.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/22.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/23.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/24.5b507eb3.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/25.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/26.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/27.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/28.5b507eb3.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/29.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/3.adfd26ad.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/30.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/31.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/32.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/33.5b507eb3.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/34.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/35.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/36.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/37.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/38.4f0f5bfe.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/39.b8244103.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/4.5c51df99.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/40.1c90b3ad.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/41.e5fc60c6.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/42.e44e5cbe.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/43.e44e5cbe.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/44.637f909c.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/45.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/46.99143bc3.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/47.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/48.442a77c9.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/49.f172cb19.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/50.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/51.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/52.5738ab9c.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/53.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/54.99ab8e0d.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/55.1030943d.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/56.3dbd987d.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/57.3dbd987d.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/58.47de02f7.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/59.86c18a36.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/60.4e72d1da.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/61.2e056650.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/62.df20f229.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/63.3ccb1c4e.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/7.c87c170a.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/8.1617aa52.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/9.250dcc31.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/css/main.eb2d52f9.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/0.88ed2e5b.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/1.088d7344.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/10.2b3ba265.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/10.2b3ba265.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/11.41e67de3.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/11.41e67de3.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/12.ea24a266.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/12.ea24a266.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/13.959adbed.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/13.959adbed.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/14.b3fd1ce9.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/14.b3fd1ce9.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/15.c9695c8d.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/15.c9695c8d.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/16.31157f32.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/16.31157f32.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/17.a3c474da.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/17.a3c474da.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/18.849f87ce.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/18.849f87ce.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/19.e5813434.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/19.e5813434.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/2.0b811919.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/20.a3b24267.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/20.a3b24267.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/21.11cffe36.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/21.11cffe36.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/22.b00e4218.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/22.b00e4218.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/23.cafd2965.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/23.cafd2965.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/24.4ef657cc.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/24.4ef657cc.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/25.3eacfd48.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/25.3eacfd48.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/26.1d8dae28.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/26.1d8dae28.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/27.5d86b6d3.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/27.5d86b6d3.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/28.950bae89.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/28.950bae89.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/29.a5f8f683.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/29.a5f8f683.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/3.dde2d5d1.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/30.2cce74bf.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/30.2cce74bf.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/31.15c7e7f9.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/31.15c7e7f9.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/32.77cb6d22.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/32.77cb6d22.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/33.4f311c89.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/33.4f311c89.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/34.24f7b139.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/34.24f7b139.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/35.6ac707c7.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/35.6ac707c7.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/36.254f092e.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/36.254f092e.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/37.0b4d8ecb.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/37.0b4d8ecb.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/38.ee712452.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/39.04baadde.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/4.760659ce.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/40.d637cdf3.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/41.0c8795cf.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/42.30a1ee79.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/43.0a290fe0.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/44.e39482f2.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/45.ce3067ee.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/46.54e8eb92.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/47.bc6e8386.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/48.a38e8355.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/49.9551a7de.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/50.756c57d6.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/51.30a3085b.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/52.18f6fda9.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/53.105ce0a8.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/54.930cbda6.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/55.3ce432c4.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/56.ecbeeb5e.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/57.fb96679b.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/58.9530dc73.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/59.5b9cbfbc.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/60.bc16b459.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/61.070afc1f.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/62.3bc6cbdc.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/63.afca1510.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/7.7b7414d0.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/7.7b7414d0.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/8.f8352a50.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/9.0c31cb0b.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/9.0c31cb0b.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/main.4b66e6fa.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/js/runtime-main.443b8290.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/bi/dist/static/media/bg.ebcb9160.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/asset-manifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/favicon.ico" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/manifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/robots.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/css/0.dc6cfdbc.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/css/1.57cc91a9.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/css/10.371c68f0.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/css/11.371c68f0.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/css/4.c87c170a.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/css/5.c4da0cff.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/css/6.72f72ba8.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/css/7.714ec016.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/css/8.c13df872.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/css/9.50cba00e.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/css/main.f5e7b744.chunk.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/js/0.6d5e221f.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/js/1.996c698e.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/js/10.774d8507.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/js/11.47687875.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/js/4.8c39b38d.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/js/4.8c39b38d.chunk.js.LICENSE.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/js/5.c2767961.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/js/6.22fb4896.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/js/7.f9f843bb.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/js/8.6756a2b9.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/js/9.f303f959.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/js/main.bc8f42f2.chunk.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/js/runtime-main.1b724c88.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/dist/static/media/bg.ebcb9160.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tool/src/layout/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/tool/src/layout/index.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "customColor": "",
  "associatedIndex": 6
}]]></component>
  <component name="ProjectId" id="31H6JNEAbro90aDPOuJe6UM8fsY" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "tool",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "yarn",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-WS-252.23892.411" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e62794dc-4320-4678-ac08-41a34dcc0770" name="更改" comment="" />
      <created>1755168577988</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755168577988</updated>
      <workItem from="1755168579616" duration="796000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>